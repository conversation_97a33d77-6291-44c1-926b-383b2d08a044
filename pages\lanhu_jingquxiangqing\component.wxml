<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <text class="error-text">{{errorMessage}}</text>
    <button bindtap="onRetryLoad" class="retry-btn">重试</button>
  </view>

  <!-- 产品详情内容 -->
  <view wx:else class="content-container">
    <view class="block_2">
      <view class="block_3">
        <view class="image-wrapper_2">
          <image src="{{lecturerDetail.avatarUrl || '../../images/lanhu_jingquxiangqing/FigmaDDSSlicePNG5a54f9d203246edd37c47deebf23e81e.png'}}" class="image_3"></image>
        </view>
        <view class="box_1">
          <view class="group_2">
            <text lines="1" class="text_2">{{lecturerDetail.name || '专业讲解员'}}</text>
          </view>
          <text lines="2" class="paragraph_1">{{lecturerDetail.intro || '暂无描述'}}</text>
        </view>
      </view>
      <view class="text-wrapper_2">
        <text lines="1" class="text_4">景区地图</text>
      </view>
    </view>
    <image src="{{productDetail.mapUrl || ''}}" class="image_4"></image>
    <!-- 指引图 -->
    <image src="{{productDetail.startListeningImageUrl || ''}}" class="block_4"></image>
    <view class="block_6">
      <view class="text-wrapper_5" style="position: relative; left: 0rpx; top: -2rpx">
        <text lines="1" class="text_7">{{productDetail.title || '讲解产品'}}</text>
      </view>
      <!-- 区域标签区域（横向滑动布局） -->
      <view wx:if="{{areaList.length > 0}}" class="area-tags-container">
        <scroll-view class="area-tags-scroll" scroll-x="true" show-scrollbar="false">
          <view class="area-tags-wrapper">
            <view wx:for="{{areaList}}" wx:key="areaId" class="area-tag-item {{selectedArea && selectedArea.areaId === item.areaId ? 'text-wrapper_6' : 'text-wrapper_7'}}" bindtap="onAreaTagClick" data-areaid="{{item.areaId}}" data-areaname="{{item.areaName}}">
              <text lines="1" class="{{selectedArea && selectedArea.areaId === item.areaId ? 'text_8' : 'text_9'}}">{{item.areaName}}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view wx:if="{{selectedArea}}" class="text-wrapper_8">
        <text lines="1" class="text_10">{{selectedArea.areaName}}</text>
      </view>
    </view>

    <!-- 音频列表区域 -->
    <view wx:if="{{audioDataList && audioDataList.length > 0}}">
      <view wx:for="{{audioDataList}}" wx:key="id" wx:for-item="audioItem" wx:for-index="audioIndex" class="block_12">
        <!-- 讲解点图 -->
        <image class="block_12" src="{{audioItem.pointImage || selectedArea.pointImage}}"></image>
        <!-- 音频板块 -->
        <view class="box_13">
          <view class="section_6">
            <view class="text-wrapper_23">
              <text lines="1" class="text_25">{{audioItem.title || 'A1-大厅'}}</text>
              <text lines="1" class="text_26">时长：{{audioItem.duration || '00:02:36'}}</text>
              <text lines="1" class="text_27">位置：{{audioItem.location || '大厅'}}</text>
            </view>
            <image src="{{audioItem.descriptionImageUrl}}" class="image_9"></image>
          </view>
          <view class="section_7">
            <image src="{{audioItem.isPlaying ? '/images/lanhu_jingquxiangqing/bofang.png' : '/images/lanhu_jingquxiangqing/bofang.png'}}"
                   class="thumbnail_6 {{audioItem.isPlaying ? 'playing' : ''}}"
                   bindtap="onAudioPlay"
                   data-audiourl="{{audioItem.audioUrl}}"
                   data-audiotitle="{{audioItem.title}}"
                   data-audioid="{{audioItem.id}}"
                   data-index="{{audioIndex}}"></image>
            <view class="block_13">
              <view class="group_8" style="width: {{audioItem.progress || 0}}%"></view>
            </view>
          </view>
          <view class="box_16"></view>
          <text lines="1" class="text_28">{{audioItem.currentTime || '00:00:00'}}/{{audioItem.duration || '00:02:36'}}</text>
        </view>
      </view>
    </view>

    <!-- 无音频数据时的提示 -->
    <view wx:else class="block_12">
      <view class="box_13">
        <view class="section_6">
          <view class="text-wrapper_23">
            <text lines="1" class="text_25">暂无音频内容</text>
            <text lines="1" class="text_26">请选择其他区域或稍后再试</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 顶部背景图和视频 -->
    <view class="block_11" style="position: absolute; left: 0rpx; top: 32rpx">
      <!-- 顶部背景图 -->
      <image class="block_11" src="{{productDetail.backgroundImageUrl || ''}}" style="position: absolute; left: 0rpx; top: 0rpx"></image>
      <view class="group_6">
        <!-- 视频 -->
        <video src="{{productDetail.exampleVideoUrl || ''}}" class="box_9"></video>
      </view>
    </view>
  </view>

  <!-- 快捷置顶 -->
  <view wx:if="{{showBackToTop}}" class="back-to-top-btn" bindtap="onBackToTop">
    <view class="image-wrapper_5">
      <image src="../../images/lanhu_jingquxiangqing/FigmaDDSSlicePNGb8f94fd6afb41d1595fb5264b9eead56.png" class="thumbnail_4"></image>
    </view>
  </view>

  <!-- 立即购买 - 固定在底部，水平居中对齐 -->
  <view class="fixed-buy-section">
    <view class="buy-content-wrapper">
      <view class="text-wrapper_18" bindtap="onBuyNow">
        <text lines="1" class="text_29">立即购买</text>
        <text lines="1" class="text_30">￥{{productDetail.price || 0}}</text>
      </view>
    </view>
  </view>

</view>