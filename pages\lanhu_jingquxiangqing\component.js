const apiService = require('../../utils/apiService.js');

Page({
  data: {
    scenicId: '',
    province: '',
    title: '',
    productId: '',           // 讲解产品ID
    productDetail: null,     // 讲解产品详情数据
    lecturerDetail: null,    // 讲师详情数据

    // 新的三层级数据结构
    areaList: [],            // 区域列表（替代categoryTags）
    selectedArea: null,      // 当前选中的区域（替代selectedCategory）
    audioDataList: [],       // 音频数据列表（替代filteredGuidePointList）

    // 保持兼容性的字段
    scenicInfo: null,        // 景区信息（保持兼容性）
    loading: true,           // 加载状态
    error: false,            // 错误状态
    errorMessage: '',        // 错误信息
    showBackToTop: false,    // 是否显示回到顶部按钮

    // 音频播放相关状态
    currentAudio: null,      // 当前播放的音频对象
    currentPlayingIndex: -1, // 当前播放的音频索引
    audioContext: null,      // 音频上下文

    // 当前音频数据和索引（用于布局显示）
    currentAudioData: {
      title: 'A1-大厅',
      duration: '00:02:36',
      location: '大厅',
      descriptionImageUrl: '',
      pointImage: '',
      audioUrl: '',
      id: '',
      isPlaying: false,
      currentTime: '00:00:00',
      progress: 0
    },
    currentAudioIndex: 0
  },

  // 页面加载时获取参数
  onLoad: function(options) {
    console.log('景区详情页面加载');
    console.log('接收到的页面参数:', options);

    this.setData({
      scenicId: options.scenicId || '',
      province: options.province || '',
      title: decodeURIComponent(options.title || ''),
      productId: options.productId || ''
    });

    // 根据参数类型加载相应数据
    if (this.data.productId) {
      // 如果有productId，加载讲解产品详情
      this.loadProductDetail();
    } else {
      // 否则加载景区详细信息（保持兼容性）
      this.loadScenicInfo();
    }
  },

  // 页面显示时
  onShow: function() {
    console.log('景区详情页面显示');
  },

  // 页面隐藏时
  onHide: function() {
    console.log('景区详情页面隐藏');
  },

  // 页面滚动监听
  onPageScroll: function(e) {
    const scrollTop = e.scrollTop;
    const showBackToTop = scrollTop > 300; // 滚动超过300px显示回到顶部按钮

    if (this.data.showBackToTop !== showBackToTop) {
      this.setData({
        showBackToTop: showBackToTop
      });
    }
  },

  // 加载讲解产品详情
  async loadProductDetail() {
      try {
        if (!this.data.productId) {
          throw new Error('产品ID不能为空');
        }

        this.setData({
          loading: true,
          error: false
        });

        console.log('开始加载讲解产品详情:', this.data.productId);

        const productDetail = await apiService.getGuideProductDetail(this.data.productId);

        console.log('讲解产品详情加载成功:', productDetail);

        this.setData({
          productDetail: productDetail,
          loading: false,
          error: false
        });

        // 加载讲师详情
        if (productDetail.lecturerId) {
          this.loadLecturerDetail(productDetail.lecturerId);
        }

        // 加载新的三层级数据
        this.loadHierarchyData();

      } catch (error) {
        console.error('加载讲解产品详情失败:', error);

        this.setData({
          loading: false,
          error: true,
          errorMessage: error.message || '加载失败，请重试'
        });

        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
  },

  // 加载景区详细信息（保持兼容性）
  loadScenicInfo: function() {
      // 这里可以根据scenicId从服务器获取详细信息
      // 目前使用模拟数据
      const mockScenicInfo = {
        id: this.data.scenicId,
        name: this.data.title,
        province: this.data.province,
        description: `这里是${this.data.title}的详细介绍...`,
        images: [],
        rating: 4.8,
        price: '免费',
        openTime: '08:00-18:00'
      };

      this.setData({
        scenicInfo: mockScenicInfo,
        loading: false
      });

      console.log('景区信息加载完成:', mockScenicInfo);
  },

  // 加载讲师详情
  async loadLecturerDetail(lecturerId) {
    try {
      if (!lecturerId) {
        console.log('讲师ID为空，跳过加载讲师详情');
        return;
      }

      console.log('开始加载讲师详情:', lecturerId);
      const lecturerDetail = await apiService.getLecturerDetail(lecturerId);

      console.log('讲师详情加载成功:', lecturerDetail);
      this.setData({
        lecturerDetail: lecturerDetail
      });

    } catch (error) {
      console.error('加载讲师详情失败:', error);
      // 讲师详情加载失败不影响主流程，只记录错误
    }
  },

  // 加载新的三层级数据（产品-区域-讲解点-音频）
  async loadHierarchyData() {
    try {
      if (!this.data.productId) {
        console.log('产品ID为空，跳过加载三层级数据');
        return;
      }

      console.log('开始加载三层级数据:', this.data.productId);
      const hierarchyData = await apiService.getCompleteHierarchyData(this.data.productId);

      console.log('三层级数据加载成功:', hierarchyData);

      // 格式化音频数据用于显示
      const formattedAudioData = this.formatAudioDataForDisplay(hierarchyData.audioData);

      this.setData({
        areaList: hierarchyData.areas || [],
        selectedArea: hierarchyData.selectedArea,
        audioDataList: formattedAudioData
      });

      // 设置第一个音频为当前显示的音频
      if (formattedAudioData.length > 0) {
        this.updateCurrentAudioData(0);
      }

      console.log('三层级数据设置完成');

    } catch (error) {
      console.error('加载三层级数据失败:', error);
      // 数据加载失败不影响主流程，只记录错误
      this.setData({
        areaList: [],
        selectedArea: null,
        audioDataList: []
      });
    }
  },

  // 重新加载数据
  onRetryLoad: function() {
      if (this.data.productId) {
        this.loadProductDetail();
      } else if (this.data.scenicId) {
        this.loadScenicInfo();
      }
  },

  // 返回上一页
  onGoBack: function() {
      wx.navigateBack();
  },

  // 立即购买
  onBuyNow: function() {
      if (!this.data.productDetail) {
        wx.showToast({
          title: '产品信息加载中',
          icon: 'none'
        });
        return;
      }

      // 这里可以实现购买逻辑
      wx.showToast({
        title: '购买功能开发中',
        icon: 'none'
      });
  },

  // 回到顶部
  onBackToTop: function() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 手动创建指南点分组
  createManualGrouping: function(guidePointList) {
    console.log('开始手动创建分组，数据数量:', guidePointList ? guidePointList.length : 0);

    if (!Array.isArray(guidePointList) || guidePointList.length === 0) {
      console.log('没有有效的指南点数据');
      return [];
    }

    const grouped = {};

    guidePointList.forEach((point, index) => {
      // 检查数据是否有效
      if (!point || typeof point !== 'object') {
        console.log(`第${index + 1}个指南点数据无效:`, point);
        return;
      }

      // 简化的数据格式化，支持多种字段名格式
      const formattedPoint = {
        id: point.id || point.pointId || point.point_id || `point_${index + 1}`,
        pointId: point.point_id || point.pointId || point.id || `point_${index + 1}`,
        title: point.title || point.name || point.pointName || `指南点${index + 1}`,
        location: point.location || point.position || point.place || '',
        audioUrl: point.audio_url || point.audioUrl || point.audio || point.audioPath || '',
        duration: parseInt(point.duration || point.time || point.audioTime || 0),
        exampleImageUrl: point.example_image_url || point.exampleImageUrl || point.exampleImage || point.imageUrl || '',
        descriptionImageUrl: point.description_image_url || point.descriptionImageUrl || point.descriptionImage || point.descImage || '',
        categoryTag: point.category_tag || point.categoryTag || point.category || point.tag || '',
        productId: point.product_id || point.productId || point.guideProductId || this.data.productId || '',
        status: point.status || 1,
        sort: parseInt(point.sort || point.order || point.sequence || index)
      };

      // 使用示例图片URL作为分组键，如果没有则使用默认分组
      const groupKey = formattedPoint.exampleImageUrl || 'default';

      if (!grouped[groupKey]) {
        grouped[groupKey] = {
          exampleImageUrl: groupKey === 'default' ? '' : groupKey,
          points: []
        };
      }

      grouped[groupKey].points.push(formattedPoint);
    });

    // 转换为数组格式，并按排序字段排序
    const result = Object.values(grouped).map(group => ({
      ...group,
      points: group.points.sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }));

    console.log(`分组完成，共${result.length}个分组，总计${result.reduce((sum, group) => sum + group.points.length, 0)}个指南点`);

    // 输出每个分组的信息
    result.forEach((group, index) => {
      console.log(`分组${index + 1}: ${group.exampleImageUrl || '默认分组'}, 包含${group.points.length}个指南点`);
    });

    return result;
  },

  // 格式化音频数据用于显示
  formatAudioDataForDisplay: function(audioData) {
    if (!Array.isArray(audioData)) {
      return [];
    }

    const formattedData = [];

    audioData.forEach(item => {
      const { point, audioList } = item;

      if (audioList && audioList.length > 0) {
        // 为每个音频创建显示项
        audioList.forEach(audio => {
          formattedData.push({
            // 保持原有的字段名以兼容现有UI
            id: audio.audioId || audio.id,
            title: audio.audioName || audio.name || '未知音频',
            duration: this.formatTime(audio.duration || 0),
            location: audio.audioAddress || audio.address || '',
            descriptionImageUrl: audio.audioImage || audio.image || '',
            audioUrl: audio.audioUrl || audio.url || '',

            // 新增字段
            pointId: point.pointId,
            pointName: point.pointName,
            pointImage: point.pointImage,
            sortOrder: audio.sortOrder || 0,

            // 播放状态字段
            isPlaying: false,
            currentTime: '00:00:00',
            progress: 0
          });
        });
      }
    });

    // 按sortOrder排序
    formattedData.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    console.log('格式化后的音频数据:', formattedData);
    return formattedData;
  },

  // 区域标签点击事件（替代原来的分类标签点击）
  onAreaTagClick: function(e) {
    const areaId = e.currentTarget.dataset.areaid;
    const areaName = e.currentTarget.dataset.areaname;
    console.log('点击区域标签:', areaId, areaName);

    // 找到选中的区域对象
    const selectedArea = this.data.areaList.find(area => area.areaId === areaId);
    if (!selectedArea) {
      console.error('未找到选中的区域');
      return;
    }

    // 更新选中的区域
    this.setData({
      selectedArea: selectedArea
    });

    // 加载该区域的音频数据
    this.loadAreaAudioData(areaId);
  },

  // 加载指定区域的音频数据
  async loadAreaAudioData(areaId) {
    try {
      console.log('加载区域音频数据:', areaId);

      wx.showLoading({ title: '加载中...' });

      const audioData = await apiService.getAreaAudioData(areaId);
      const formattedAudioData = this.formatAudioDataForDisplay(audioData);

      this.setData({
        audioDataList: formattedAudioData
      });

      wx.hideLoading();
      console.log('区域音频数据加载完成');

    } catch (error) {
      console.error('加载区域音频数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 音频播放事件 - 支持播放/暂停切换
  onAudioPlay: function(e) {
    const audioUrl = e.currentTarget.dataset.audiourl;
    const audioTitle = e.currentTarget.dataset.audiotitle;
    const audioId = e.currentTarget.dataset.audioid;
    const index = parseInt(e.currentTarget.dataset.index);

    console.log('点击音频:', audioTitle, audioUrl, '索引:', index);

    if (!audioUrl) {
      wx.showToast({
        title: '音频地址无效',
        icon: 'none'
      });
      return;
    }

    // 如果点击的是当前正在播放的音频，则暂停
    if (this.data.currentPlayingIndex === index && this.data.audioContext) {
      this.pauseAudio();
      return;
    }

    // 如果有其他音频在播放，先停止
    if (this.data.audioContext) {
      this.stopAudio();
    }

    // 开始播放新音频
    this.playAudio(audioUrl, audioTitle, audioId, index);

    // 更新当前音频数据显示
    this.updateCurrentAudioData(index);
  },

  // 播放音频
  playAudio: function(audioUrl, audioTitle, audioId, index) {
    console.log('开始播放音频:', audioTitle);

    // 创建音频上下文实例
    const audioContext = wx.createInnerAudioContext();
    audioContext.src = audioUrl;

    // 更新播放状态
    this.setData({
      audioContext: audioContext,
      currentPlayingIndex: index
    });

    // 更新音频列表中的播放状态
    this.updateAudioPlayingState(index, true);

    // 监听音频开始播放事件
    audioContext.onPlay(() => {
      console.log('音频开始播放:', audioTitle);
      wx.showToast({
        title: '开始播放',
        icon: 'none',
        duration: 1000
      });
    });

    // 监听音频播放进度
    audioContext.onTimeUpdate(() => {
      const currentTime = audioContext.currentTime;
      const duration = audioContext.duration;
      const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

      // 更新进度显示
      this.updateAudioProgress(index, currentTime, progress);
    });

    // 监听音频播放结束事件
    audioContext.onEnded(() => {
      console.log('音频播放结束:', audioTitle);
      this.stopAudio();
    });

    // 监听音频错误事件
    audioContext.onError((error) => {
      console.error('音频播放出错:', error);
      wx.showToast({
        title: '播放失败',
        icon: 'none'
      });
      this.stopAudio();
    });

    // 开始播放
    audioContext.play();
  },

  // 暂停音频
  pauseAudio: function() {
    if (this.data.audioContext) {
      this.data.audioContext.pause();
      this.updateAudioPlayingState(this.data.currentPlayingIndex, false);

      wx.showToast({
        title: '已暂停',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 停止音频
  stopAudio: function() {
    if (this.data.audioContext) {
      this.data.audioContext.stop();
      this.data.audioContext.destroy();

      // 重置播放状态
      this.updateAudioPlayingState(this.data.currentPlayingIndex, false);
      this.setData({
        audioContext: null,
        currentPlayingIndex: -1
      });
    }
  },

  // 更新音频播放状态
  updateAudioPlayingState: function(index, isPlaying) {
    if (index < 0 || index >= this.data.audioDataList.length) return;

    const audioDataList = [...this.data.audioDataList];
    audioDataList[index] = {
      ...audioDataList[index],
      isPlaying: isPlaying
    };

    this.setData({
      audioDataList: audioDataList
    });
  },

  // 更新音频播放进度
  updateAudioProgress: function(index, currentTime, progress) {
    if (index < 0 || index >= this.data.audioDataList.length) return;

    const audioDataList = [...this.data.audioDataList];
    audioDataList[index] = {
      ...audioDataList[index],
      currentTime: this.formatTime(currentTime),
      progress: progress
    };

    this.setData({
      audioDataList: audioDataList
    });

    // 如果是当前显示的音频，同时更新当前音频数据
    if (index === this.data.currentAudioIndex) {
      this.setData({
        'currentAudioData.currentTime': this.formatTime(currentTime),
        'currentAudioData.progress': progress
      });
    }
  },

  // 切换到下一个音频
  switchToNextAudio: function() {
    const nextIndex = (this.data.currentAudioIndex + 1) % this.data.audioDataList.length;
    if (nextIndex < this.data.audioDataList.length) {
      this.updateCurrentAudioData(nextIndex);
    }
  },

  // 切换到上一个音频
  switchToPrevAudio: function() {
    const prevIndex = this.data.currentAudioIndex > 0 ?
      this.data.currentAudioIndex - 1 :
      this.data.audioDataList.length - 1;
    this.updateCurrentAudioData(prevIndex);
  },

  // 格式化时间显示
  formatTime: function(seconds) {
    if (!seconds || seconds < 0) return '00:00:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 更新当前音频数据显示
  updateCurrentAudioData: function(index) {
    if (index < 0 || index >= this.data.audioDataList.length) return;

    const audioData = this.data.audioDataList[index];
    this.setData({
      currentAudioData: {
        title: audioData.title || 'A1-大厅',
        duration: audioData.duration || '00:02:36',
        location: audioData.location || '大厅',
        descriptionImageUrl: audioData.descriptionImageUrl || '',
        pointImage: audioData.pointImage || '',
        audioUrl: audioData.audioUrl || '',
        id: audioData.id || '',
        isPlaying: audioData.isPlaying || false,
        currentTime: audioData.currentTime || '00:00:00',
        progress: audioData.progress || 0
      },
      currentAudioIndex: index
    });
  },

  // 页面隐藏时停止音频播放
  onHide: function() {
    console.log('景区详情页面隐藏');
    this.stopAudio();
  },

  // 页面卸载时停止音频播放
  onUnload: function() {
    console.log('景区详情页面卸载');
    this.stopAudio();
  }


});
